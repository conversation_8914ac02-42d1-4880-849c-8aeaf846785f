**Your task is to implement a "reset password" feature with the endpoint `/v1/auth_krakend/reset-password`**

## **Flow Overview:**
1. Client sends email → Backend validates email exists → Generate & send OTP code
2. <PERSON><PERSON> sends OTP code + verification token → Backend validates → Generate reset token
3. <PERSON><PERSON> sends new password + reset token → Backend validates & updates password

---

## **Step 1: Email Validation & OTP Generation**

**Endpoint:** `POST /v1/auth_krakend/reset-password/request`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Logic:**
1. Check if email exists in `accounts` table
2. If exists, generate 6-digit OTP code
3. Save to `auth_otp` table with 3-minute expiry
4. Send email via `scheduled_message` table
5. Return verification token to client

**Database Operations:**
- Query `accounts` table: `SELECT id FROM accounts WHERE email = ? AND is_active = 1`
- Save to `auth_otp` table (use existing pattern)
- Save to `scheduled_message` table for email delivery

**Response:**
```json
{
  "success": true,
  "message": "Reset code sent to your email",
  "verification_token": "encrypted_token_here",
  "expires_in": 180
}
```

---

## **Step 2: OTP Verification**

**Endpoint:** `POST /v1/auth_krakend/reset-password/verify`

**Request Body:**
```json
{
  "code": "123456",
  "verification_token": "token_from_step1"
}
```

**Logic:**
1. Decrypt verification token to get OTP record ID
2. Retrieve OTP record from `auth_otp`
3. Verify code matches (hash comparison)
4. Check expiry time
5. Generate reset token and save to `users_session`
6. Mark OTP as authenticated

**Reset Token Strategy:**
```go
// Generate unique session ID and reset token
sessionID := uuid.New().String()
resetToken := utils.GenerateSecureToken(32)

// Hash the reset token for database storage
hashedToken, _ := utils.HashPasswordWithCost(resetToken, 10)

// Save to users_session
expiredAt := time.Now().Add(time.Minute * 15).Unix()
saveToSession(sessionID, hashedToken, expiredAt)

// Return combined token to client (sessionID:resetToken encrypted)
clientToken := utils.Encrypt(sessionID+":"+resetToken, models.KEY_RESET)
```

**Response:**
```json
{
  "success": true,
  "message": "Code verified successfully",
  "reset_token": "encrypted_session_and_token",
  "expires_in": 900
}
```

---

## **Step 3: Password Reset**

**Endpoint:** `POST /v1/auth_krakend/reset-password/update`

**Request Body:**
```json
{
  "new_password": "newSecurePassword123!",
  "confirm_password": "newSecurePassword123!",
  "reset_token": "token_from_step2"
}
```

**Logic:**
1. Decrypt reset token to get `sessionID:resetToken`
2. Query `users_session` by sessionID
3. Verify reset token matches (hash comparison)
4. Check expiry time
5. Validate password requirements
6. Update password in `accounts` table
7. Clean up session and OTP records

**Token Verification:**
```go
// Decrypt client token
decrypted := utils.Decrypt(clientToken, models.KEY_RESET)
parts := strings.Split(decrypted, ":")
sessionID := parts[0]
resetToken := parts[1]

// Get session from database
session := getSession(sessionID)

// Verify token
if !utils.CheckPasswordHash(resetToken, session.Token) {
    return errors.New("invalid reset token")
}
```

---

## **Database Schema Usage:**

**auth_otp table:**
- `contact`: User's email
- `contact_type`: 'email'
- `token`: Encrypted hashed OTP code
- `authenticated_at`: Set when OTP is verified
- `date_expired`: 3 minutes from creation

**users_session table:**
- `id`: Unique session ID (UUID)
- `token`: Hashed reset token
- `expired_at`: 15 minutes from creation
- `data`: Store email for reference (optional)

**scheduled_message table:**
- `title`: "Password Reset Code"
- `message`: "Your reset code is: {CODE}"
- `receiver`: User's email
- `media`: 'email'
- `time_deliver`: Immediate (current timestamp)

---

## **Security Considerations:**

1. **Rate Limiting:** Max 3 requests per email per hour
2. **Token Expiry:** OTP (3 min), Reset token (15 min)
3. **Single Use:** Tokens become invalid after successful use
4. **Password Requirements:** Minimum 8 chars, mixed case, numbers, symbols
5. **Cleanup:** Remove expired records periodically

## **Error Responses:**

```json
{
  "success": false,
  "error_code": "EMAIL_NOT_FOUND",
  "message": "Email address not registered"
}
```

**Error Codes:**
- `EMAIL_NOT_FOUND`: Email doesn't exist
- `INVALID_CODE`: Wrong OTP code
- `CODE_EXPIRED`: OTP has expired
- `TOKEN_INVALID`: Invalid reset token
- `TOKEN_EXPIRED`: Reset token expired
- `PASSWORD_WEAK`: Password doesn't meet requirements
- `RATE_LIMITED`: Too many requests

---

## **Additional Requirements:**

1. Log all reset attempts for security monitoring
2. Send email confirmation after successful password reset
3. Invalidate all existing user sessions after password change
4. Add request validation middleware
5. Include request ID for tracking

**Questions for Implementation:**
- Should we allow multiple concurrent reset tokens per user?
- Do you want to notify users of failed reset attempts?
- Should we implement account lockout after multiple failed attempts?

---
