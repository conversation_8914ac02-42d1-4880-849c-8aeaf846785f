package domain

// Reset Password Input/Output structures

// ResetPasswordInput represents the input for reset password requests
type ResetPasswordInput struct {
	Email            string `json:"email,omitempty" validate:"email"`
	VerificationToken string `json:"verification_token,omitempty"`
	OTPCode          string `json:"otp_code,omitempty" validate:"len=6,numeric"`
	ResetToken       string `json:"reset_token,omitempty"`
	NewPassword      string `json:"new_password,omitempty" validate:"min=8"`
	ConfirmPassword  string `json:"confirm_password,omitempty"`
}

// ResetPasswordResponse represents the response for reset password requests
type ResetPasswordResponse struct {
	Success           bool   `json:"success"`
	Message           string `json:"message"`
	VerificationToken string `json:"verification_token,omitempty"`
	ResetToken        string `json:"reset_token,omitempty"`
	ErrorCode         string `json:"error_code,omitempty"`
	Details           string `json:"details,omitempty"`
}

// OTPRecord represents an OTP record from auth_otp table
type OTPRecord struct {
	AuthOtpId       int64  `json:"auth_otp_id"`
	Contact         string `json:"contact"`
	ContactType     string `json:"contact_type"`
	Token           string `json:"token"`
	OtpCode         string `json:"otp_code"`
	AuthenticatedAt *int64 `json:"authenticated_at"`
	DateCreated     int64  `json:"date_created"`
	DateExpired     int64  `json:"date_expired"`
}

// ResetSession represents a reset session from users_session table
type ResetSession struct {
	Id        string `json:"id"`
	Email     string `json:"email"`
	Token     string `json:"token"`
	CreatedAt int64  `json:"created_at"`
	ExpiredAt int64  `json:"expired_at"`
}

// ErrorCodes for reset password
const (
	ErrorEmailNotFound      = "EMAIL_NOT_FOUND"
	ErrorEmailRateLimited   = "EMAIL_RATE_LIMITED"
	ErrorInvalidToken       = "INVALID_TOKEN"
	ErrorTokenExpired       = "TOKEN_EXPIRED"
	ErrorInvalidOTP         = "INVALID_OTP"
	ErrorOTPExpired         = "OTP_EXPIRED"
	ErrorTooManyAttempts    = "TOO_MANY_ATTEMPTS"
	ErrorPasswordTooWeak    = "PASSWORD_TOO_WEAK"
	ErrorPasswordsDontMatch = "PASSWORDS_DONT_MATCH"
	ErrorInternalError      = "INTERNAL_ERROR"
)

// Error messages
const (
	MsgEmailNotFound        = "Email not registered"
	MsgEmailRateLimited     = "Too many requests for this email. Please try again later"
	MsgInvalidToken         = "Invalid or malformed token"
	MsgTokenExpired         = "Token has expired"
	MsgInvalidOTP           = "Invalid OTP code"
	MsgOTPExpired           = "OTP code has expired"
	MsgTooManyAttempts      = "Maximum verification attempts exceeded"
	MsgPasswordTooWeak      = "Password doesn't meet security requirements"
	MsgPasswordsDontMatch   = "New password and confirmation don't match"
	MsgInternalError        = "Internal server error occurred"
	MsgVerificationSent     = "Verification code sent to email"
	MsgCodeVerified         = "Code verified successfully"
	MsgPasswordUpdated      = "Password updated successfully"
)
