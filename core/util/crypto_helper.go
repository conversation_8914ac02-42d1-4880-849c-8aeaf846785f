package util

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"regexp"
	"strconv"
	"strings"
)

// GenerateOTP generates a secure 6-digit OTP code
func GenerateOTP() (string, error) {
	// Generate 3 random bytes (24 bits)
	bytes := make([]byte, 3)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	
	// Convert to integer and ensure it's 6 digits
	num := int(bytes[0])<<16 | int(bytes[1])<<8 | int(bytes[2])
	otp := num % 1000000
	
	// Pad with leading zeros if necessary
	return fmt.Sprintf("%06d", otp), nil
}

// HashSHA256 creates a SHA-256 hash of the input string
func HashSHA256(text string) string {
	hash := sha256.Sum256([]byte(text))
	return hex.EncodeToString(hash[:])
}

// EncryptAES256GCM encrypts text using AES-256-GCM
func EncryptAES256GCM(plaintext, key string) (string, error) {
	// Ensure key is 32 bytes for AES-256
	keyBytes := []byte(key)
	if len(keyBytes) < 32 {
		// Pad key with zeros if too short
		padded := make([]byte, 32)
		copy(padded, keyBytes)
		keyBytes = padded
	} else if len(keyBytes) > 32 {
		// Truncate key if too long
		keyBytes = keyBytes[:32]
	}

	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptAES256GCM decrypts text using AES-256-GCM
func DecryptAES256GCM(ciphertext, key string) (string, error) {
	// Ensure key is 32 bytes for AES-256
	keyBytes := []byte(key)
	if len(keyBytes) < 32 {
		// Pad key with zeros if too short
		padded := make([]byte, 32)
		copy(padded, keyBytes)
		keyBytes = padded
	} else if len(keyBytes) > 32 {
		// Truncate key if too long
		keyBytes = keyBytes[:32]
	}

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New("ciphertext too short")
	}

	nonce, cipherData := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// CreateVerificationToken creates an encrypted verification token
func CreateVerificationToken(authOtpId int64, timestamp int64, encryptionKey string) (string, error) {
	payload := fmt.Sprintf("%d:%d", authOtpId, timestamp)
	return EncryptAES256GCM(payload, encryptionKey)
}

// DecryptVerificationToken decrypts and parses a verification token
func DecryptVerificationToken(token, encryptionKey string) (authOtpId int64, timestamp int64, err error) {
	payload, err := DecryptAES256GCM(token, encryptionKey)
	if err != nil {
		return 0, 0, err
	}

	parts := strings.Split(payload, ":")
	if len(parts) != 2 {
		return 0, 0, errors.New("invalid token format")
	}

	authOtpId, err = strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return 0, 0, err
	}

	timestamp, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return 0, 0, err
	}

	return authOtpId, timestamp, nil
}

// CreateResetToken creates an encrypted reset token
func CreateResetToken(sessionId string, timestamp int64, encryptionKey string) (string, error) {
	payload := fmt.Sprintf("%s:%d", sessionId, timestamp)
	return EncryptAES256GCM(payload, encryptionKey)
}

// DecryptResetToken decrypts and parses a reset token
func DecryptResetToken(token, encryptionKey string) (sessionId string, timestamp int64, err error) {
	payload, err := DecryptAES256GCM(token, encryptionKey)
	if err != nil {
		return "", 0, err
	}

	parts := strings.Split(payload, ":")
	if len(parts) != 2 {
		return "", 0, errors.New("invalid token format")
	}

	sessionId = parts[0]
	timestamp, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return "", 0, err
	}

	return sessionId, timestamp, nil
}

// ValidatePassword validates password strength
func ValidatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}

	// Check for uppercase letter
	if matched, _ := regexp.MatchString(`[A-Z]`, password); !matched {
		return errors.New("password must contain at least one uppercase letter")
	}

	// Check for lowercase letter
	if matched, _ := regexp.MatchString(`[a-z]`, password); !matched {
		return errors.New("password must contain at least one lowercase letter")
	}

	// Check for number
	if matched, _ := regexp.MatchString(`[0-9]`, password); !matched {
		return errors.New("password must contain at least one number")
	}

	// Check for special character
	if matched, _ := regexp.MatchString(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`, password); !matched {
		return errors.New("password must contain at least one special character")
	}

	return nil
}

// GenerateSecureID generates a cryptographically secure random ID
func GenerateSecureID(length int) (string, error) {
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
