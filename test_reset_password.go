package main

import (
	"fmt"
	"os"
	"time"
	"uniqdev/api-pos-web/core/util"
	"uniqdev/api-pos-web/domain"
)

func main() {
	fmt.Println("Testing Reset Password Implementation...")
	
	// Test 1: OTP Generation
	fmt.Println("\n1. Testing OTP Generation:")
	for i := 0; i < 5; i++ {
		otp, err := util.GenerateOTP()
		if err != nil {
			fmt.Printf("Error generating OTP: %v\n", err)
			return
		}
		fmt.Printf("Generated OTP: %s\n", otp)
	}
	
	// Test 2: Password Validation
	fmt.Println("\n2. Testing Password Validation:")
	passwords := []string{
		"weak",
		"StrongPass123!",
		"NoSpecial123",
		"nouppercasepass123!",
		"NOLOWERCASE123!",
		"NoNumbers!",
	}
	
	for _, pwd := range passwords {
		err := util.ValidatePassword(pwd)
		if err != nil {
			fmt.Printf("Password '%s': INVALID - %s\n", pwd, err.Error())
		} else {
			fmt.Printf("Password '%s': VALID\n", pwd)
		}
	}
	
	// Test 3: Encryption/Decryption
	fmt.Println("\n3. Testing Encryption/Decryption:")
	encryptionKey := "test-32-character-encryption-key"
	
	// Test verification token
	authOtpId := int64(123)
	timestamp := time.Now().Unix()
	
	verificationToken, err := util.CreateVerificationToken(authOtpId, timestamp, encryptionKey)
	if err != nil {
		fmt.Printf("Error creating verification token: %v\n", err)
		return
	}
	fmt.Printf("Created verification token: %s\n", verificationToken)
	
	decryptedOtpId, decryptedTimestamp, err := util.DecryptVerificationToken(verificationToken, encryptionKey)
	if err != nil {
		fmt.Printf("Error decrypting verification token: %v\n", err)
		return
	}
	
	if decryptedOtpId == authOtpId && decryptedTimestamp == timestamp {
		fmt.Println("Verification token encryption/decryption: SUCCESS")
	} else {
		fmt.Printf("Verification token encryption/decryption: FAILED - got %d:%d, expected %d:%d\n", 
			decryptedOtpId, decryptedTimestamp, authOtpId, timestamp)
	}
	
	// Test reset token
	sessionId := "test-session-123"
	resetToken, err := util.CreateResetToken(sessionId, timestamp, encryptionKey)
	if err != nil {
		fmt.Printf("Error creating reset token: %v\n", err)
		return
	}
	fmt.Printf("Created reset token: %s\n", resetToken)
	
	decryptedSessionId, decryptedResetTimestamp, err := util.DecryptResetToken(resetToken, encryptionKey)
	if err != nil {
		fmt.Printf("Error decrypting reset token: %v\n", err)
		return
	}
	
	if decryptedSessionId == sessionId && decryptedResetTimestamp == timestamp {
		fmt.Println("Reset token encryption/decryption: SUCCESS")
	} else {
		fmt.Printf("Reset token encryption/decryption: FAILED - got %s:%d, expected %s:%d\n", 
			decryptedSessionId, decryptedResetTimestamp, sessionId, timestamp)
	}
	
	// Test 4: Hashing
	fmt.Println("\n4. Testing Hashing:")
	testString := "test-string-to-hash"
	hash := util.HashSHA256(testString)
	fmt.Printf("SHA-256 hash of '%s': %s\n", testString, hash)
	
	// Test bcrypt
	password := "TestPassword123!"
	hashedPassword, err := util.BcryptHash(password)
	if err != nil {
		fmt.Printf("Error hashing password: %v\n", err)
		return
	}
	fmt.Printf("Bcrypt hash of '%s': %s\n", password, hashedPassword)
	
	err = util.BcryptVerify(password, hashedPassword)
	if err != nil {
		fmt.Printf("Bcrypt verification: FAILED - %v\n", err)
	} else {
		fmt.Println("Bcrypt verification: SUCCESS")
	}
	
	// Test 5: Domain structures
	fmt.Println("\n5. Testing Domain Structures:")
	
	// Test Phase 1 request
	phase1Input := domain.ResetPasswordInput{
		Email: "<EMAIL>",
	}
	fmt.Printf("Phase 1 input: %+v\n", phase1Input)
	
	// Test Phase 2 request
	phase2Input := domain.ResetPasswordInput{
		VerificationToken: verificationToken,
		OTPCode:          "123456",
	}
	fmt.Printf("Phase 2 input: %+v\n", phase2Input)
	
	// Test Phase 3 request
	phase3Input := domain.ResetPasswordInput{
		ResetToken:      resetToken,
		NewPassword:     "NewPassword123!",
		ConfirmPassword: "NewPassword123!",
	}
	fmt.Printf("Phase 3 input: %+v\n", phase3Input)
	
	// Test response structure
	response := domain.ResetPasswordResponse{
		Success:           true,
		Message:           domain.MsgVerificationSent,
		VerificationToken: verificationToken,
	}
	fmt.Printf("Response: %+v\n", response)
	
	fmt.Println("\n✅ All tests completed successfully!")
	fmt.Println("\nTo use the reset password feature:")
	fmt.Println("1. Set RESET_PASSWORD_ENCRYPTION_KEY in your .env file")
	fmt.Println("2. Make sure your database has the required tables (accounts, auth_otp, users_session, scheduled_message)")
	fmt.Println("3. Send POST requests to /v1/auth_krakend/reset-password with appropriate payloads for each phase")
	
	// Show example curl commands
	fmt.Println("\nExample API calls:")
	fmt.Println("Phase 1 (Email Request):")
	fmt.Println(`curl -X POST http://localhost:3000/v1/auth_krakend/reset-password \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'`)
	
	fmt.Println("\nPhase 2 (OTP Verification):")
	fmt.Println(`curl -X POST http://localhost:3000/v1/auth_krakend/reset-password \
  -H "Content-Type: application/json" \
  -d '{"verification_token":"TOKEN_FROM_PHASE_1","otp_code":"123456"}'`)
	
	fmt.Println("\nPhase 3 (Password Update):")
	fmt.Println(`curl -X POST http://localhost:3000/v1/auth_krakend/reset-password \
  -H "Content-Type: application/json" \
  -d '{"reset_token":"TOKEN_FROM_PHASE_2","new_password":"NewPass123!","confirm_password":"NewPass123!"}'`)
}
